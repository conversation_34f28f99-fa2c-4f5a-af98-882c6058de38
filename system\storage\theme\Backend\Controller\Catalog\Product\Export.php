<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Export extends \Theme25\ControllerSubMethods {

    private $supportedFormats = [];
    private $unavailableFormats = [];
    private $formatRequirements = [];
    private $batchSize = 150;
    private $languageMapping = [];
    private $categoryCache = [];
    private $selectedCategories = [];
    private $selectedProducts = [];

    public function __construct($registry) {
        parent::__construct($registry);
        $this->checkAvailableFormats();
        $this->initializeLanguageMapping();
        $this->initializeCategoryCache();
    }

    /**
     * Проверява кои формати за експорт са налични
     */
    private function checkAvailableFormats() {
        $this->supportedFormats = [];
        $this->unavailableFormats = [];
        $this->formatRequirements = [];

        // CSV - винаги наличен (вграден в PHP)
        $this->supportedFormats[] = 'csv';
        $this->formatRequirements['csv'] = [
            'name' => 'CSV',
            'description' => 'Comma Separated Values (.csv)',
            'icon' => 'ri-file-text-line',
            'available' => true,
            'reason' => 'Вграден в PHP'
        ];

        // XML - проверка за simplexml разширение
        if (extension_loaded('simplexml')) {
            $this->supportedFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => true,
                'reason' => 'SimpleXML разширение активно'
            ];
        } else {
            $this->unavailableFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => false,
                'reason' => 'Липсва SimpleXML разширение'
            ];
        }

        // XLSX - проверка за PhpSpreadsheet или алтернативни библиотеки
        $xlsxAvailable = false;
        $xlsxReason = '';

        // Проверка за PhpOffice\PhpSpreadsheet
        if (class_exists('PhpOffice\\PhpSpreadsheet\\IOFactory')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet библиотека';
        }
        // Проверка за алтернативна библиотека SimpleXLSX
        elseif (class_exists('SimpleXLSX')) {
            $xlsxAvailable = true;
            $xlsxReason = 'SimpleXLSX библиотека';
        }
        // Проверка дали файлът съществува в проекта
        elseif (file_exists(DIR_SYSTEM . 'storage/vendor/phpoffice/phpspreadsheet/src/PhpSpreadsheet/IOFactory.php')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet (локално)';
        }
        else {
            $xlsxReason = 'Липсва PhpOffice\\PhpSpreadsheet или SimpleXLSX библиотека';
        }

        if ($xlsxAvailable) {
            $this->supportedFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => true,
                'reason' => $xlsxReason
            ];
        } else {
            $this->unavailableFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => false,
                'reason' => $xlsxReason
            ];
        }

        // Логиране на резултатите
        F()->log->developer('Available export formats: ' . implode(', ', $this->supportedFormats), __FILE__, __LINE__);
        if (!empty($this->unavailableFormats)) {
            F()->log->developer('Unavailable export formats: ' . implode(', ', $this->unavailableFormats), __FILE__, __LINE__);
        }
    }

    /**
     * Инициализира мапинга на езиците
     */
    private function initializeLanguageMapping() {
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();

        foreach ($languages as $language) {
            $this->languageMapping[$language['code']] = $language['language_id'];
        }
    }

    /**
     * Инициализира кеша на категориите
     */
    private function initializeCategoryCache() {
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadCategoryPaths();
    }

    /**
     * Зарежда всички категории с техните пътища в кеша
     */
    private function loadCategoryPaths() {
        $language_id = $this->getLanguageId();

        $sql = "SELECT
                    cp.category_id,
                    GROUP_CONCAT(cd.name ORDER BY cp.level SEPARATOR ' > ') AS path
                FROM `" . DB_PREFIX . "category_path` cp
                LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (cp.path_id = cd.category_id)
                WHERE cd.language_id = '{$language_id}'
                GROUP BY cp.category_id
                ORDER BY cp.category_id";

        $query = $this->db->query($sql);

        foreach ($query->rows as $row) {
            $this->categoryCache[$row['category_id']] = $row['path'];
        }
    }

    /**
     * Основен метод за показване на формата за експорт
     */
    public function execute() {
        $this->setTitle('Експорт на продукти');
        $this->initAdminData();
        $this->addBackendScriptWithVersion('product-export.js', 'footer');
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_export');
    }

    /**
     * Подготвя данните за формата
     */
    public function prepareData() {
        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product'),
            'supported_formats' => $this->supportedFormats,
            'unavailable_formats' => $this->unavailableFormats,
            'format_requirements' => $this->formatRequirements,
            'available_formats' => array_filter($this->formatRequirements, function($format) {
                return $format['available'];
            }),
            'languages' => $this->languageMapping,
            'is_developer' => isDeveloper()
        ]);
    }

    /**
     * Обработва експорта на продукти
     */
    public function process() {
        $json = [];

        try {
            // Debug информация за разработчици
            if (isDeveloper()) {
                F()->log->developer('process called', __FILE__, __LINE__);
                F()->log->developer('POST data: ' . print_r($_POST, true), __FILE__, __LINE__);
            }

            // Валидация на заявката
            if (!$this->validateExportRequest()) {
                throw new \Exception('Невалидна заявка за експорт');
            }

            // Получаване на параметрите за експорт
            $exportFormat = $_POST['export_format'] ?? 'csv';
            $this->selectedCategories = $_POST['selected_categories'] ?? [];
            $this->selectedProducts = $_POST['selected_products'] ?? [];

            // Валидация на формата
            if (!in_array($exportFormat, $this->supportedFormats)) {
                throw new \Exception('Неподдържан формат за експорт: ' . $exportFormat);
            }

            // Зареждане на съответния модел за експорт
            $exportModel = $this->loadExportModel($exportFormat);

            // Получаване на продуктите за експорт
            $products = $this->getProductsForExport();

            if (empty($products)) {
                throw new \Exception('Няма продукти за експорт с избраните критерии');
            }

            // Генериране на експорт файла
            $result = $this->generateExportFile($exportModel, $products, $exportFormat);

            $json['success'] = 'Експортът завърши успешно';
            $json['download_url'] = $result['download_url'];
            $json['statistics'] = $result['statistics'];

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
            F()->log->developer('Export error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира заявката за експорт
     */
    private function validateExportRequest() {

        if (!isset($_POST['export_format'])) {
            return false;
        }

        $exportFormat = $_POST['export_format'];

        if (!in_array($exportFormat, $this->supportedFormats)) {
            // Проверяваме дали форматът е в недостъпните формати
            if (in_array($exportFormat, $this->unavailableFormats)) {
                $formatInfo = $this->formatRequirements[$exportFormat] ?? null;
                $reason = $formatInfo ? $formatInfo['reason'] : 'Неизвестна причина';
                throw new \Exception("Форматът {$exportFormat} не е наличен в момента. Причина: {$reason}");
            } else {
                $supportedList = implode(', ', array_map('strtoupper', $this->supportedFormats));
                throw new \Exception("Неподдържан файлов формат '{$exportFormat}'. Поддържани формати: {$supportedList}");
            }
        }

        return true;
    }

    /**
     * Зарежда съответния модел за експорт
     */
    private function loadExportModel($format) {
        $modelClass = 'Theme25\\Backend\\Model\\Catalog\\ProductExport' . ucfirst($format);
        $modelPath = 'catalog/product_export_' . $format;

        $this->loadModelAs($modelPath, 'exportModel');

        return $this->exportModel;
    }

    /**
     * Получава продуктите за експорт според избраните критерии
     */
    private function getProductsForExport() {
        $products = [];
        $productIds = [];

        // Ако са избрани категории, получаваме продуктите от тях
        if (!empty($this->selectedCategories)) {
            foreach ($this->selectedCategories as $categoryId) {
                $categoryProducts = $this->getProductsFromCategory($categoryId);
                foreach ($categoryProducts as $product) {
                    $productIds[] = $product['product_id'];
                }
            }
        }

        // Ако са избрани конкретни продукти, добавяме ги
        if (!empty($this->selectedProducts)) {
            $productIds = array_merge($productIds, $this->selectedProducts);
        }

        // Ако няма избрани критерии, експортираме всички продукти
        if (empty($productIds)) {
            $productIds = $this->getAllProductIds();
        }

        // Премахваме дублиращи се ID-та
        $productIds = array_unique($productIds);

        // Получаваме пълните данни за продуктите
        if (!empty($productIds)) {
            $products = $this->getProductsData($productIds);
        }

        return $products;
    }

    /**
     * Получава продуктите от дадена категория (включително подкатегории)
     */
    private function getProductsFromCategory($categoryId) {
        // Получаваме всички подкатегории рекурсивно
        $allCategoryIds = $this->getAllSubcategories($categoryId);

        if (empty($allCategoryIds)) {
            return [];
        }

        $categoryIdsStr = implode(',', array_map('intval', $allCategoryIds));

        $sql = "SELECT DISTINCT p.product_id
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
                WHERE ptc.category_id IN ({$categoryIdsStr})
                AND p.status = '1'
                ORDER BY p.product_id";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * Получава всички подкатегории на дадена категория рекурсивно
     */
    private function getAllSubcategories($categoryId) {
        $categories = [$categoryId]; // Включваме основната категория

        // Получаваме директните подкатегории
        $sql = "SELECT category_id FROM `" . DB_PREFIX . "category`
                WHERE parent_id = '{$categoryId}' AND status = '1'";

        $result = $this->db->query($sql);

        foreach ($result->rows as $row) {
            // Рекурсивно получаваме подкатегориите на всяка подкатегория
            $subcategories = $this->getAllSubcategories($row['category_id']);
            $categories = array_merge($categories, $subcategories);
        }

        // Премахваме дублиращи се ID-та и връщаме уникални стойности
        return array_unique($categories);
    }

    /**
     * Получава ID-тата на всички продукти
     */
    private function getAllProductIds() {
        $sql = "SELECT product_id FROM `" . DB_PREFIX . "product` WHERE status = '1' ORDER BY product_id";
        $query = $this->db->query($sql);

        return array_column($query->rows, 'product_id');
    }

    /**
     * Получава пълните данни за продуктите
     */
    private function getProductsData($productIds) {
        if (empty($productIds)) {
            return [];
        }

        $products = [];
        $batches = array_chunk($productIds, $this->batchSize);

        foreach ($batches as $batch) {
            $batchProducts = $this->getProductsBatch($batch);
            $products = array_merge($products, $batchProducts);
        }

        return $products;
    }

    /**
     * Получава данните за порция от продукти
     */
    private function getProductsBatch($productIds) {
        $productIdsStr = implode(',', array_map('intval', $productIds));
        $language_id = $this->getLanguageId();

        // Основна заявка за продуктите
        $sql = "SELECT
                    p.*,
                    pd.name,
                    pd.description,
                    pd.tag,
                    pd.meta_title,
                    pd.meta_description,
                    pd.meta_keyword
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id)
                WHERE p.product_id IN ({$productIdsStr})
                AND pd.language_id = '{$language_id}'
                ORDER BY p.product_id";

        $query = $this->db->query($sql);
        $products = [];

        foreach ($query->rows as $row) {
            $product = $row;

            // Добавяме категориите
            $product['product_category'] = $this->getProductCategories($row['product_id']);

            // Добавяме опциите
            $product['product_options'] = $this->getProductOptions($row['product_id']);

            // Добавяме атрибутите
            $product['product_attributes'] = $this->getProductAttributes($row['product_id']);

            // Добавяме многоезичните данни
            $product['product_description'] = $this->getProductDescriptions($row['product_id']);

            $products[] = $product;
        }

        return $products;
    }

    /**
     * Получава категориите на продукт
     */
    private function getProductCategories($productId) {
        $sql = "SELECT ptc.category_id
                FROM `" . DB_PREFIX . "product_to_category` ptc
                WHERE ptc.product_id = '{$productId}'";

        $query = $this->db->query($sql);
        $categories = [];

        foreach ($query->rows as $row) {
            $categoryId = $row['category_id'];
            if (isset($this->categoryCache[$categoryId])) {
                $categories[] = $this->categoryCache[$categoryId];
            }
        }

        return implode(',', $categories);
    }

    /**
     * Получава опциите на продукт
     */
    private function getProductOptions($productId) {
        $language_id = $this->getLanguageId();

        $sql = "SELECT
                    po.option_id,
                    po.required,
                    od.name as option_name,
                    o.type as option_type
                FROM `" . DB_PREFIX . "product_option` po
                LEFT JOIN `" . DB_PREFIX . "option` o ON (po.option_id = o.option_id)
                LEFT JOIN `" . DB_PREFIX . "option_description` od ON (o.option_id = od.option_id)
                WHERE po.product_id = '{$productId}'
                AND od.language_id = '{$language_id}'";

        $query = $this->db->query($sql);
        $options = [];

        foreach ($query->rows as $row) {
            $option = [
                'option_name' => $row['option_name'],
                'option_type' => $row['option_type'],
                'required' => $row['required'],
                'values' => []
            ];

            // Получаваме стойностите на опцията
            $valuesSql = "SELECT ovd.name
                         FROM `" . DB_PREFIX . "product_option_value` pov
                         LEFT JOIN `" . DB_PREFIX . "option_value_description` ovd ON (pov.option_value_id = ovd.option_value_id)
                         WHERE pov.product_id = '{$productId}'
                         AND pov.option_id = '{$row['option_id']}'
                         AND ovd.language_id = '{$language_id}'";

            $valuesQuery = $this->db->query($valuesSql);
            foreach ($valuesQuery->rows as $valueRow) {
                $option['values'][] = $valueRow['name'];
            }

            $options[] = $option;
        }

        return $options;
    }

    /**
     * Получава атрибутите на продукт
     */
    private function getProductAttributes($productId) {
        $language_id = $this->getLanguageId();

        $sql = "SELECT
                    ad.name as attribute_name,
                    pa.text as attribute_value,
                    agd.name as attribute_group
                FROM `" . DB_PREFIX . "product_attribute` pa
                LEFT JOIN `" . DB_PREFIX . "attribute` a ON (pa.attribute_id = a.attribute_id)
                LEFT JOIN `" . DB_PREFIX . "attribute_description` ad ON (a.attribute_id = ad.attribute_id)
                LEFT JOIN `" . DB_PREFIX . "attribute_group` ag ON (a.attribute_group_id = ag.attribute_group_id)
                LEFT JOIN `" . DB_PREFIX . "attribute_group_description` agd ON (ag.attribute_group_id = agd.attribute_group_id)
                WHERE pa.product_id = '{$productId}'
                AND pa.language_id = '{$language_id}'
                AND ad.language_id = '{$language_id}'
                AND agd.language_id = '{$language_id}'";

        $query = $this->db->query($sql);
        $attributes = [];

        foreach ($query->rows as $row) {
            $attributes[] = [
                'attribute_name' => $row['attribute_name'],
                'attribute_value' => $row['attribute_value'],
                'attribute_group' => $row['attribute_group']
            ];
        }

        return $attributes;
    }

    /**
     * Получава многоезичните описания на продукт
     */
    private function getProductDescriptions($productId) {
        $sql = "SELECT
                    pd.*,
                    l.code as language_code
                FROM `" . DB_PREFIX . "product_description` pd
                LEFT JOIN `" . DB_PREFIX . "language` l ON (pd.language_id = l.language_id)
                WHERE pd.product_id = '{$productId}'";

        $query = $this->db->query($sql);
        $descriptions = [];

        foreach ($query->rows as $row) {
            $descriptions[$row['language_code']] = [
                'name' => $row['name'],
                'description' => $row['description'],
                'tag' => $row['tag'],
                'meta_title' => $row['meta_title'],
                'meta_description' => $row['meta_description'],
                'meta_keyword' => $row['meta_keyword']
            ];
        }

        return $descriptions;
    }

    /**
     * Генерира експорт файла
     */
    private function generateExportFile($exportModel, $products, $format) {
        // Създаваме директорията за експорт ако не съществува
        $exportDir = DIR_UPLOAD . 'export/';
        if (!is_dir($exportDir)) {
            mkdir($exportDir, 0755, true);
        }

        // Генерираме име на файла
        $fileName = 'export_products_' . date('Y-m-d_H-i-s') . '.' . $format;
        $filePath = $exportDir . $fileName;

        // Генерираме файла чрез модела
        $exportModel->generateFile($products, $filePath);

        // Подготвяме статистиките
        $statistics = [
            'total_exported' => count($products),
            'file_name' => $fileName,
            'file_size' => filesize($filePath),
            'format' => strtoupper($format)
        ];

        // Генерираме URL за сваляне
        $downloadUrl = HTTP_CATALOG . 'upload/export/' . $fileName;

        return [
            'download_url' => $downloadUrl,
            'statistics' => $statistics
        ];
    }

    /**
     * Autocomplete за категории - използва същата логика като основния Category контролер
     */
    public function autocompleteCategory() {
        $json = [];

        if (isset($_GET['filter_name'])) {
            $this->loadModelAs('catalog/category', 'categoryModel');

            // Определяме лимита от заявката или използваме стандартния
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

            // Ограничаваме максималния лимит за безопасност
            if ($limit > 1000) {
                $limit = 1000;
            }

            $filter_data = [
                'filter_name' => $_GET['filter_name'],
                'sort'        => 'name',
                'order'       => 'ASC',
                'start'       => 0,
                'limit'       => $limit
            ];

            $results = $this->categoryModel->getCategories($filter_data);

            foreach ($results as $result) {
                $json[] = [
                    'category_id' => $result['category_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
                ];
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Autocomplete за продукти - използва същата логика като ProductAutocomplete
     */
    public function autocompleteProduct() {
        $json = [];

        $this->loadModelAs('catalog/product', 'productModel');

        $filter_data = [
            'start' => 0,
            'limit' => isset($_GET['limit']) ? (int)$_GET['limit'] : 10
        ];

        if (isset($_GET['filter_name'])) {
            $filter_data['filter_name'] = $_GET['filter_name'];
        }

        $results = $this->productModel->getProducts($filter_data);

        foreach ($results as $result) {
            // Подготвяне на изображението
            $thumb = '';
            if (!empty($result['image'])) {
                $this->loadModelAs('tool/Imageservice', 'imageService');
                $image_details = $this->imageService->getImageDetailsByPath($result['image'], 48, 48);
                $thumb = $image_details['resized_image_url'];
            } else {
                $this->loadModelAs('tool/image', 'imageModel');
                $thumb = $this->imageModel->resize('no_image.png', 48, 48);
            }

            $json[] = [
                'product_id' => $result['product_id'],
                'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8')),
                'model' => $result['model'],
                'price' => $result['price'],
                'thumb' => $thumb
            ];
        }

        $this->setJSONResponseOutput($json);
    }
}
