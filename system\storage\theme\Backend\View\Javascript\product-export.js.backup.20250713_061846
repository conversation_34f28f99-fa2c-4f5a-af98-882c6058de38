/**
 * Модул за експорт на продукти
 * Базиран на product-promotion.js за автозавършване
 */
(function() {
    'use strict';

    const ProductExportModule = {
        // Конфигурация
        config: {
            debounceDelay: 500,
            maxConcurrentRequests: 10,
            exportUrl: 'index.php?route=catalog/product/export&method=processExport'
        },

        // Състояние
        state: {
            activeRequests: new Set(),
            requestQueue: [],
            selectedCategories: new Map(),
            selectedProducts: new Map()
        },

        // Инициализация
        init() {
            this.bindEvents();
            this.initializeAutocomplete();
            this.initializeExportForm();
        },

        // Свързване на събития
        bindEvents() {
            // Форма за експорт
            const exportForm = document.getElementById('export-form');
            if (exportForm) {
                exportForm.addEventListener('submit', (e) => this.handleExportSubmit(e));
            }

            // Бутони за избор на всички/никой
            const selectAllCategoriesBtn = document.getElementById('select-all-categories');
            const clearCategoriesBtn = document.getElementById('clear-categories');
            const selectAllProductsBtn = document.getElementById('select-all-products');
            const clearProductsBtn = document.getElementById('clear-products');

            if (selectAllCategoriesBtn) {
                selectAllCategoriesBtn.addEventListener('click', () => this.selectAllCategories());
            }
            if (clearCategoriesBtn) {
                clearCategoriesBtn.addEventListener('click', () => this.clearCategories());
            }
            if (selectAllProductsBtn) {
                selectAllProductsBtn.addEventListener('click', () => this.selectAllProducts());
            }
            if (clearProductsBtn) {
                clearProductsBtn.addEventListener('click', () => this.clearProducts());
            }
        },

        // Инициализация на автозавършване
        initializeAutocomplete() {
            this.initializeCategoryAutocomplete();
            this.initializeProductAutocomplete();
        },

        // Автозавършване за категории (множествен избор)
        initializeCategoryAutocomplete() {
            const categoryInput = document.getElementById('category-autocomplete');
            const categoryResults = document.getElementById('category-results');
            const selectedCategoriesContainer = document.getElementById('selected-categories');

            if (!categoryInput || !categoryResults || !selectedCategoriesContainer) return;

            let debounceTimer;
            let currentController;

            categoryInput.addEventListener('input', (e) => {
                clearTimeout(debounceTimer);

                if (currentController) {
                    currentController.abort();
                }

                debounceTimer = setTimeout(() => {
                    this.searchCategories(e.target.value, categoryResults);
                }, this.config.debounceDelay);
            });

            categoryInput.addEventListener('focus', () => {
                if (categoryInput.value.trim() === '') {
                    this.searchCategories('', categoryResults);
                }
            });

            // Скриване на резултатите при клик извън тях
            document.addEventListener('click', (e) => {
                if (!categoryInput.contains(e.target) && !categoryResults.contains(e.target)) {
                    categoryResults.classList.add('hidden');
                }
            });
        },

        // Автозавършване за продукти (множествен избор)
        initializeProductAutocomplete() {
            const productInput = document.getElementById('product-autocomplete');
            const productResults = document.getElementById('product-results');
            const selectedProductsContainer = document.getElementById('selected-products');

            if (!productInput || !productResults || !selectedProductsContainer) return;

            let debounceTimer;
            let currentController;

            productInput.addEventListener('input', (e) => {
                clearTimeout(debounceTimer);
                
                if (currentController) {
                    currentController.abort();
                }

                debounceTimer = setTimeout(() => {
                    this.searchProducts(e.target.value, productResults);
                }, this.config.debounceDelay);
            });

            productInput.addEventListener('focus', () => {
                if (productInput.value.trim() === '') {
                    this.searchProducts('', productResults);
                }
            });

            // Скриване на резултатите при клик извън тях
            document.addEventListener('click', (e) => {
                if (!productInput.contains(e.target) && !productResults.contains(e.target)) {
                    productResults.classList.add('hidden');
                }
            });
        },

        // Търсене на категории
        searchCategories(query, resultsContainer) {
            if (this.state.activeRequests.size >= this.config.maxConcurrentRequests) {
                this.state.requestQueue.push(() => this.searchCategories(query, resultsContainer));
                return;
            }

            const controller = new AbortController();
            this.state.activeRequests.add(controller);

            const userToken = this.getUserToken();
            const timestamp = Date.now();
            const currentQuery = query.trim();
            const limit = currentQuery === '' ? 10 : 1000;

            let fetchUrl = `index.php?route=catalog/category/autocomplete&filter_name=${encodeURIComponent(currentQuery)}&limit=${limit}&user_token=${userToken}&_=${timestamp}`;

            fetch(fetchUrl, {
                signal: controller.signal,
                cache: 'no-store',
                headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
            })
            .then(response => {
                if (!response.ok) throw new Error('Грешка при зареждане на категории');
                return response.json();
            })
            .then(data => {
                this.displayCategoryResults(data, resultsContainer);
            })
            .catch(error => {
                if (error.name !== 'AbortError') {
                    console.error('Грешка при търсене на категории:', error);
                    this.showError('Грешка при зареждане на категории');
                }
            })
            .finally(() => {
                this.state.activeRequests.delete(controller);
                this.processQueue();
            });
        },

        // Търсене на продукти
        searchProducts(query, resultsContainer) {
            if (this.state.activeRequests.size >= this.config.maxConcurrentRequests) {
                this.state.requestQueue.push(() => this.searchProducts(query, resultsContainer));
                return;
            }

            const controller = new AbortController();
            this.state.activeRequests.add(controller);

            const userToken = this.getUserToken();
            const timestamp = Date.now();
            const currentQuery = query.trim();
            const limit = currentQuery === '' ? 10 : 1000;

            let fetchUrl = `index.php?route=catalog/product/autocomplete&type=product&filter_name=${encodeURIComponent(currentQuery)}&limit=${limit}&user_token=${userToken}&_=${timestamp}`;

            fetch(fetchUrl, {
                signal: controller.signal,
                cache: 'no-store',
                headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
            })
            .then(response => {
                if (!response.ok) throw new Error('Грешка при зареждане на продукти');
                return response.json();
            })
            .then(data => {
                this.displayProductResults(data, resultsContainer);
            })
            .catch(error => {
                if (error.name !== 'AbortError') {
                    console.error('Грешка при търсене на продукти:', error);
                    this.showError('Грешка при зареждане на продукти');
                }
            })
            .finally(() => {
                this.state.activeRequests.delete(controller);
                this.processQueue();
            });
        },

        // Показване на резултати за категории
        displayCategoryResults(data, container) {
            container.innerHTML = '';

            if (!Array.isArray(data) || data.length === 0) {
                const noResults = document.createElement('div');
                noResults.className = 'p-2 text-gray-500 autocomplete-no-results';
                noResults.textContent = 'Няма намерени категории';
                container.appendChild(noResults);
            } else {
                data.forEach(category => {
                    const item = document.createElement('div');
                    item.className = 'p-2 cursor-pointer hover:bg-gray-100 autocomplete-suggestion';
                    item.textContent = category.name;
                    item.dataset.id = category.category_id;

                    item.addEventListener('click', () => {
                        this.addCategory({
                            id: category.category_id,
                            name: category.name
                        });
                        container.classList.add('hidden');
                        document.getElementById('category-autocomplete').value = '';
                    });

                    container.appendChild(item);
                });
            }

            container.classList.remove('hidden');
            console.log('Category results shown:', container.children.length, 'items');
        },

        // Показване на резултати за продукти
        displayProductResults(data, container) {
            container.innerHTML = '';

            if (!Array.isArray(data) || data.length === 0) {
                const noResults = document.createElement('div');
                noResults.className = 'p-2 text-sm text-red-500';
                noResults.textContent = 'Няма намерени продукти';
                container.appendChild(noResults);
            } else {
                let hasVisibleProducts = false;

                data.forEach(product => {
                    // Проверяваме дали продуктът вече е избран
                    const isSelected = this.state.selectedProducts.has(product.product_id);

                    if (!isSelected) {
                        hasVisibleProducts = true;
                        const item = document.createElement('div');
                        item.className = 'p-2 cursor-pointer hover:bg-gray-100 flex items-center space-x-2';

                        item.innerHTML = `
                            <img src="${product.thumb}" alt="${this.escapeHtml(product.name)}" class="w-8 h-8 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium">${this.escapeHtml(product.name)}</div>
                                <div class="text-xs text-gray-500">${this.escapeHtml(product.model)}</div>
                            </div>
                        `;

                        item.addEventListener('click', () => {
                            this.addProduct(product);
                            container.classList.add('hidden');
                            document.getElementById('product-autocomplete').value = '';
                        });

                        container.appendChild(item);
                    }
                });

                if (!hasVisibleProducts) {
                    const noResults = document.createElement('div');
                    noResults.className = 'p-2 text-sm text-gray-500';
                    noResults.textContent = 'Всички намерени продукти са вече избрани';
                    container.appendChild(noResults);
                }
            }

            container.classList.remove('hidden');
            console.log('Product results shown:', container.children.length, 'items');
        },

        // Добавяне на категория
        addCategory(category) {
            if (this.state.selectedCategories.has(category.id)) {
                this.showError('Тази категория вече е избрана');
                return;
            }

            this.state.selectedCategories.set(category.id, category);
            this.updateSelectedCategoriesDisplay();
        },

        // Добавяне на продукт
        addProduct(product) {
            if (this.state.selectedProducts.has(product.product_id)) {
                this.showError('Този продукт вече е избран');
                return;
            }

            this.state.selectedProducts.set(product.product_id, product);
            this.updateSelectedProductsDisplay();
        },

        // Обновяване на показването на избраните категории
        updateSelectedCategoriesDisplay() {
            const container = document.getElementById('selected-categories');
            if (!container) return;

            container.innerHTML = '';

            this.state.selectedCategories.forEach((category, id) => {
                const item = document.createElement('div');
                item.className = 'flex items-center space-x-2 p-2 bg-blue-50 rounded border border-blue-200 mb-2';
                item.innerHTML = `
                    <div class="flex-1">
                        <div class="text-sm font-medium text-blue-800">${this.escapeHtml(category.name)}</div>
                        <div class="text-xs text-blue-600">Избрана категория</div>
                    </div>
                    <button type="button" class="text-blue-500 hover:text-blue-700 p-1" data-id="${id}">
                        <i class="ri-close-line"></i>
                    </button>
                `;

                const removeBtn = item.querySelector('button');
                removeBtn.addEventListener('click', () => {
                    this.state.selectedCategories.delete(id);
                    this.updateSelectedCategoriesDisplay();
                });

                container.appendChild(item);
            });

            this.updateCategoryCount();
        },

        // Обновяване на показването на избраните продукти
        updateSelectedProductsDisplay() {
            const container = document.getElementById('selected-products');
            if (!container) return;

            container.innerHTML = '';

            this.state.selectedProducts.forEach((product, id) => {
                const item = document.createElement('div');
                item.className = 'flex items-center space-x-2 p-2 bg-green-50 rounded border border-green-200 mb-2';
                item.innerHTML = `
                    <img src="${product.thumb || ''}" alt="${this.escapeHtml(product.name)}" class="w-8 h-8 object-cover rounded">
                    <div class="flex-1">
                        <div class="text-sm font-medium text-green-800">${this.escapeHtml(product.name)}</div>
                        <div class="text-xs text-green-600">Модел: ${this.escapeHtml(product.model)}</div>
                    </div>
                    <button type="button" class="text-green-500 hover:text-green-700 p-1" data-id="${id}">
                        <i class="ri-close-line"></i>
                    </button>
                `;

                const removeBtn = item.querySelector('button');
                removeBtn.addEventListener('click', () => {
                    this.state.selectedProducts.delete(id);
                    this.updateSelectedProductsDisplay();
                });

                container.appendChild(item);
            });

            this.updateProductCount();
        },

        // Обновяване на броя категории
        updateCategoryCount() {
            const countElement = document.getElementById('category-count');
            if (countElement) {
                countElement.textContent = this.state.selectedCategories.size;
            }
        },

        // Обновяване на броя продукти
        updateProductCount() {
            const countElement = document.getElementById('product-count');
            if (countElement) {
                countElement.textContent = this.state.selectedProducts.size;
            }
        },

        // Избор на всички категории
        selectAllCategories() {
            this.searchCategories('', document.getElementById('category-results'));
            // Тази функционалност може да бъде разширена за автоматично добавяне
        },

        // Изчистване на категориите
        clearCategories() {
            this.state.selectedCategories.clear();
            this.updateSelectedCategoriesDisplay();
        },

        // Избор на всички продукти
        selectAllProducts() {
            this.searchProducts('', document.getElementById('product-results'));
            // Тази функционалност може да бъде разширена за автоматично добавяне
        },

        // Изчистване на продуктите
        clearProducts() {
            this.state.selectedProducts.clear();
            this.updateSelectedProductsDisplay();
        },

        // Инициализация на формата за експорт
        initializeExportForm() {
            const formatRadios = document.querySelectorAll('input[name="export_format"]');
            formatRadios.forEach(radio => {
                radio.addEventListener('change', () => this.updateFormatInfo());
            });

            this.updateFormatInfo();
        },

        // Обновяване на информацията за формата
        updateFormatInfo() {
            const selectedFormat = document.querySelector('input[name="export_format"]:checked');
            const infoElement = document.getElementById('format-info');

            if (!selectedFormat || !infoElement) return;

            const format = selectedFormat.value;
            let info = '';

            switch (format) {
                case 'csv':
                    info = 'CSV формат е подходящ за импорт в Excel и други табличи програми.';
                    break;
                case 'xml':
                    info = 'XML формат запазва структурата на данните и е подходящ за системна интеграция.';
                    break;
                case 'xlsx':
                    info = 'XLSX формат е родният формат на Microsoft Excel.';
                    break;
            }

            infoElement.textContent = info;
        },

        // Обработка на изпращането на формата
        handleExportSubmit(e) {
            e.preventDefault();

            const form = e.target;
            const formData = new FormData(form);

            // Добавяме избраните категории
            const categoryIds = Array.from(this.state.selectedCategories.keys());
            categoryIds.forEach(id => {
                formData.append('selected_categories[]', id);
            });

            // Добавяме избраните продукти
            const productIds = Array.from(this.state.selectedProducts.keys());
            productIds.forEach(id => {
                formData.append('selected_products[]', id);
            });

            // Валидация
            if (categoryIds.length === 0 && productIds.length === 0) {
                this.showError('Моля изберете поне една категория или продукт за експорт');
                return;
            }

            this.performExport(formData);
        },

        // Изпълнение на експорта
        performExport(formData) {
            const submitBtn = document.getElementById('export-submit-btn');
            const originalText = submitBtn ? submitBtn.textContent : '';

            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Експортиране...';
            }

            // user_token вече е включен във формата, не е нужно да се добавя отново

            fetch(this.config.exportUrl, {
                method: 'POST',
                body: formData,
                cache: 'no-store',
                headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    this.showError(data.error);
                } else if (data.success) {
                    this.showSuccess(data.success);
                    if (data.download_url) {
                        this.initiateDownload(data.download_url);
                    }
                    if (data.statistics) {
                        this.showStatistics(data.statistics);
                    }
                }
            })
            .catch(error => {
                console.error('Грешка при експорт:', error);
                this.showError('Възникна грешка при експорта');
            })
            .finally(() => {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                }
            });
        },

        // Стартиране на сваляне на файла
        initiateDownload(url) {
            const link = document.createElement('a');
            link.href = url;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        // Показване на статистики
        showStatistics(stats) {
            const statsContainer = document.getElementById('export-statistics');
            if (!statsContainer) return;

            statsContainer.innerHTML = `
                <div class="alert alert-info">
                    <h5>Статистики за експорта:</h5>
                    <ul>
                        <li>Експортирани продукти: ${stats.total_exported}</li>
                        <li>Формат: ${stats.format}</li>
                        <li>Име на файл: ${stats.file_name}</li>
                        <li>Размер на файл: ${this.formatFileSize(stats.file_size)}</li>
                    </ul>
                </div>
            `;
            statsContainer.style.display = 'block';
        },

        // Форматиране на размера на файла
        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // Обработка на опашката със заявки
        processQueue() {
            if (this.state.requestQueue.length > 0 && this.state.activeRequests.size < this.config.maxConcurrentRequests) {
                const nextRequest = this.state.requestQueue.shift();
                nextRequest();
            }
        },

        // Получаване на user token
        getUserToken() {
            const tokenElement = document.querySelector('input[name="user_token"]');
            return tokenElement ? tokenElement.value : '';
        },

        // Escape HTML
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // Показване на грешка
        showError(message) {
            this.showAlert(message, 'danger');
        },

        // Показване на успех
        showSuccess(message) {
            this.showAlert(message, 'success');
        },

        // Показване на alert
        showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alert-container') || document.body;

            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            alertContainer.appendChild(alert);

            // Автоматично премахване след 5 секунди
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }
    };

    // Инициализация при зареждане на DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => ProductExportModule.init());
    } else {
        ProductExportModule.init();
    }

    // Експортиране на модула за глобален достъп
    window.ProductExportModule = ProductExportModule;

})();
